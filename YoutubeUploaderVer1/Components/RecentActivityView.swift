//
//  RecentActivityView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 24/06/25.
//


import SwiftUI
import SwiftData
import AVKit
import UniformTypeIdentifiers
import AppKit

struct RecentActivityView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \RecentActivityItem.createdDate, order: .reverse) private var recentItems: [RecentActivityItem]
    @StateObject private var activityManager = RecentActivityManager()
    @State private var selectedContentType: ContentType? = nil
    @State private var showDeleteConfirmation = false
    @State private var itemToDelete: RecentActivityItem?
    
    var filteredItems: [RecentActivityItem] {
        if let selectedType = selectedContentType {
            return recentItems.filter { $0.contentType == selectedType }
        }
        return recentItems
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header Section
            headerSection
            
            // Content Filter Section
            filterSection
            
            // Main Content
            if filteredItems.isEmpty {
                emptyStateView
            } else {
                contentGridView
            }
        }
        .padding(24)
        .padding(.top,0)
        .onAppear {
            activityManager.setModelContext(modelContext)
            activityManager.cleanupExpiredContent()
        }
        .alert("Delete Item", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Delete", role: .destructive) {
                if let item = itemToDelete {
                    activityManager.deleteItem(item)
                }
            }
        } message: {
            Text("Are you sure you want to delete this item? This action cannot be undone.")
        }
    }
    
    // MARK: - Header Section
//    private var headerSection: some View {
//        VStack(alignment: .leading, spacing: 8) {
//            HStack {
//                Image(systemName: "clock.fill")
//                    .font(AppFontStyle.title1.style)
//                    .foregroundColor(AppColor.youtubeRed.color)
//                
//                VStack(alignment: .leading, spacing: 4) {
//                    Text("Recent Activity")
//                        .font(AppFontStyle.largeTitle.style.weight(.bold))
//                        .foregroundColor(AppColor.textPrimary.color)
//                    
//                    Text("Your generated content from the last 7 days")
//                        .font(AppFontStyle.subheadline.style)
//                        .foregroundColor(AppColor.textSecondary.color)
//                }
//                
//                Spacer()
//                
//                // Stats Badge
//                HStack(spacing: 12) {
//                    VStack(alignment: .center, spacing: 2) {
//                        Text("\(recentItems.count)")
//                            .font(AppFontStyle.title2.style.weight(.bold))
//                            .foregroundColor(AppColor.youtubeRed.color)
//                        Text("Total Items")
//                            .font(AppFontStyle.caption2.style)
//                            .foregroundColor(AppColor.textSecondary.color)
//                    }
//                    .padding(.horizontal, 16)
//                    .padding(.vertical, 8)
//                    .background(
//                        RoundedRectangle(cornerRadius: 12)
//                            .fill(AppColor.youtubeRed.color.opacity(0.1))
//                    )
//                }
//            }
//            
//            Divider()
//                .background(AppColor.borderPrimary.color.opacity(0.2))
//                .padding(.top, 16)
//        }
//        .padding(.bottom, 20)
//    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Title and Subtitle (pure text like modernHeaderSection)
            VStack(alignment: .leading, spacing: 8) {
                Text("Recent Activity")
                    .font(AppFontStyle.largeTitle.style)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("Your generated content from the last 7 days")
                    .font(AppFontStyle.title2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textSecondary.color)
            }

           
        }
        .padding(.bottom, 24)
    }

    // MARK: - Filter Section
    private var filterSection: some View {
        HStack(spacing: 12) {
            Text("Filter:")
                .font(AppFontStyle.subheadline.style.weight(.medium))
                .foregroundColor(AppColor.textSecondary.color)
            
            // All Items Button
            Button(action: {
                selectedContentType = nil
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "square.grid.2x2")
                        .font(AppFontStyle.caption1.style)
                    Text("All")
                        .font(AppFontStyle.caption1.style.weight(.medium))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(selectedContentType == nil ? AppColor.youtubeRed.color : AppColor.surfaceSecondary.color)
                )
                .foregroundColor(selectedContentType == nil ? .white : AppColor.textSecondary.color)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Content Type Filters
            ForEach(ContentType.allCases, id: \.self) { contentType in
                Button(action: {
                    selectedContentType = selectedContentType == contentType ? nil : contentType
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: contentType.iconName)
                            .font(AppFontStyle.caption1.style)
                        Text(contentType.displayName)
                            .font(AppFontStyle.caption1.style.weight(.medium))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedContentType == contentType ? AppColor.youtubeRed.color : AppColor.surfaceSecondary.color)
                    )
                    .foregroundColor(selectedContentType == contentType ? .white : AppColor.textSecondary.color)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Spacer()
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "clock.badge.questionmark")
                .font(.system(size: 64))
                .foregroundColor(AppColor.textSecondary.color.opacity(0.5))
            
            VStack(spacing: 8) {
                Text("No Recent Activity")
                    .font(AppFontStyle.title2.style.weight(.semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text("Generate videos or scripts to see them here.\nContent is automatically saved for 7 days.")
                    .font(AppFontStyle.body.style)
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Content Grid
    private var contentGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16)
            ], spacing: 16) {
                ForEach(filteredItems) { item in
                    RecentActivityCard(
                        item: item,
                        onDelete: {
                            itemToDelete = item
                            showDeleteConfirmation = true
                        }
                    )
                }
            }
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Recent Activity Card
struct RecentActivityCard: View {
    let item: RecentActivityItem
    let onDelete: () -> Void

    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with type icon and actions
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: item.contentType.iconName)
                        .font(AppFontStyle.callout.style.weight(.medium))
                        .foregroundColor(AppColor.accentBlue.color)

                    Text(item.contentType.displayName)
                        .font(AppFontStyle.caption1.style.weight(.semibold))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(AppColor.surfaceTertiary.color)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppColor.borderSecondary.color, lineWidth: 1)
                        )
                )

                Spacer()

                // Actions Menu
                Menu {
                    Button("Download") {
                        downloadContent(item)
                    }
                    .help("Download \(item.contentType.displayName)")

                    Divider()

                    Button("Delete", role: .destructive) {
                        onDelete()
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .font(AppFontStyle.title3.style)
                        .foregroundColor(AppColor.textSecondary.color)
                        .frame(width: 32, height: 32)
                        
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Content Preview
            contentPreview
            
            // Metadata
            VStack(alignment: .leading, spacing: 8) {
                Text(item.title)
                    .font(AppFontStyle.subheadline.style.weight(.semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                    .lineLimit(2)

                HStack {
                    HStack(spacing: 4) {
                        Image(systemName: "calendar")
                            .font(.system(size: 11))
                            .foregroundColor(AppColor.textTertiary.color)
                        
                        Text(item.formattedDate)
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(AppColor.textSecondary.color)
                    }

                    Spacer()

                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.system(size: 11))
                            .foregroundColor(item.daysUntilExpiration > 0 ? AppColor.warningOrange.color : AppColor.errorRed.color)
                        
                        if item.daysUntilExpiration > 0 {
                            Text("\(item.daysUntilExpiration)d left")
                                .font(AppFontStyle.caption2.style.weight(.medium))
                                .foregroundColor(AppColor.warningOrange.color)
                        } else {
                            Text("Expires soon")
                                .font(AppFontStyle.caption2.style.weight(.medium))
                                .foregroundColor(AppColor.errorRed.color)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill((item.daysUntilExpiration > 0 ? AppColor.warningOrange.color : AppColor.errorRed.color).opacity(0.1))
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    AppColor.borderPrimary.color.opacity(0.3),
                                    AppColor.borderSecondary.color.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )

    }
    
    @ViewBuilder
    private var contentPreview: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    colors: [
                        AppColor.surfaceSecondary.color,
                        AppColor.surfaceTertiary.color
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(height: 140)
            .overlay {
                if item.contentType == .video {
                    if let thumbnailData = item.thumbnail {
                        VideoPlayer(player: AVPlayer(url: item.fileURL!))
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    } else {
                        VStack(spacing: 12) {
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 40))
                                .foregroundColor(AppColor.accentBlue.color)
                            
                            if let duration = item.formattedDuration {
                                Text(duration)
                                    .font(AppFontStyle.caption2.style.weight(.medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(AppColor.surfacePrimary.color)
                                    )
                            }
                        }
                    }
                } else {
                    VStack(spacing: 12) {
                        Image(systemName: "doc.text.fill")
                            .font(.system(size: 40))
                            .foregroundColor(AppColor.accentPurple.color)
                        
                        if let wordCount = item.wordCount {
                            Text("\(wordCount) words")
                                .font(AppFontStyle.caption2.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(AppColor.surfacePrimary.color)
                                )
                        }
                    }
                }
            }
            .overlay(
                // Duration badge for videos (bottom right)
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        if item.contentType == .video, let duration = item.formattedDuration {
                            Text(duration)
                                .font(.system(size: 11, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(.black.opacity(0.7))
                                )
                        }
                    }
                    .padding(.bottom, 8)
                    .padding(.trailing, 8)
                }
            )
    }
    
    // MARK: - Download Functions
    /// Downloads content based on type (video file or script content)
    private func downloadContent(_ item: RecentActivityItem) {
        let savePanel = NSSavePanel()
        savePanel.canCreateDirectories = true

        if item.contentType == .video {
            // Download video file
            guard let fileURL = item.fileURL else {
                print("❌ Video file not found")
                return
            }

            savePanel.allowedContentTypes = [.mpeg4Movie, .quickTimeMovie, .movie]
            savePanel.nameFieldStringValue = "generated_clip.mp4"
            savePanel.title = "Save Video Clip"

            if savePanel.runModal() == .OK, let destinationURL = savePanel.url {
                do {
                    try FileManager.default.copyItem(at: fileURL, to: destinationURL)
                    print("✅ Video saved to \(destinationURL)")
                    NSWorkspace.shared.activateFileViewerSelecting([destinationURL])
                } catch {
                    print("❌ Video download failed: \(error.localizedDescription)")
                }
            }
        } else {
            // Download script content
            guard let scriptContent = item.content else {
                print("❌ Script content not found")
                return
            }

            savePanel.allowedContentTypes = [.plainText]
            savePanel.nameFieldStringValue = "script_\(item.title.replacingOccurrences(of: " ", with: "_")).txt"
            savePanel.title = "Save Script"

            if savePanel.runModal() == .OK, let destinationURL = savePanel.url {
                // Create formatted script with metadata header
                let header = """
                YouTube Video Script
                Generated on: \(item.formattedDate)

                Title: \(item.title)
                Type: \(item.metadata["type"] ?? "Unknown")
                Length: \(item.metadata["length"] ?? "Unknown")
                Tone: \(item.metadata["tone"] ?? "Unknown")
                Target Audience: \(item.metadata["targetAudience"] ?? "General")

                ═══════════════════════════════════════════════════════════════

                """

                let fullScript = header + scriptContent

                do {
                    try fullScript.write(to: destinationURL, atomically: true, encoding: .utf8)
                    print("✅ Script saved to \(destinationURL)")
                    NSWorkspace.shared.activateFileViewerSelecting([destinationURL])
                } catch {
                    print("❌ Script download failed: \(error.localizedDescription)")
                }
            }
        }
    }
}

