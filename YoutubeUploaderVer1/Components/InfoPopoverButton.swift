import SwiftUI

struct InfoPopoverButton<Content: View>: View {
    let content: () -> Content
    var iconSize: CGFloat = 15
    var iconColor: Color = AppColor.accentBlue.color
    var popoverWidth: CGFloat = 320
    var popoverTitle: String? = nil

    @State private var showPopover = false

    var body: some View {
        Image(systemName: "info.circle")
            .font(.system(size: iconSize, weight: .medium))
            .foregroundColor(iconColor)
            .onHover { hovering in
                #if os(macOS)
                showPopover = hovering
                #endif
            }
            .popover(isPresented: $showPopover, arrowEdge: .bottom) {
                VStack(alignment: .leading, spacing: 10) {
                    if let popoverTitle = popoverTitle {
                        Text(popoverTitle)
                            .font(.headline)
                            .foregroundColor(AppColor.textPrimary.color)
                    }
                    content()
                }
                .padding(18)
                .frame(width: popoverWidth)
            }
    }
}

// MARK: - String Convenience
extension InfoPopoverButton where Content == Text {
    init(text: String, iconSize: CGFloat = 15, iconColor: Color = AppColor.accentBlue.color, popoverWidth: CGFloat = 320, popoverTitle: String? = nil) {
        self.content = { Text(text)
            .font(.body)
            .foregroundColor(AppColor.textSecondary.color)
            .fixedSize(horizontal: false, vertical: true)
        }
        self.iconSize = iconSize
        self.iconColor = iconColor
        self.popoverWidth = popoverWidth
        self.popoverTitle = popoverTitle
    }
}
