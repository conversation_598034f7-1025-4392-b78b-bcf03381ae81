//
//  AIOptionsCard.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import SwiftUI
enum AIOption: String, CaseIterable, Identifiable {
    case videoSummarization
    case shortsClips
    case contentRecreation
    case contentFreshness
    case performancePredictor




    var id: String { self.rawValue }
    
    var title: String {
        switch self {
        case .videoSummarization: return "Video Summarization"
        case .shortsClips: return "Shorts & Clips Creation"
        case .contentRecreation: return "Content Recreation"
        case .contentFreshness: return "Content Freshness Score"
        case .performancePredictor: return "Performance Predictor"
        }
    }

    var subtitle: String {
        switch self {
        case .videoSummarization: return "Generate concise video summaries"
        case .shortsClips: return "Generate short-form clips"
        case .contentRecreation: return "Transform into different formats"
        case .contentFreshness: return "Analyze content uniqueness and originality"
        case .performancePredictor: return "Predict video performance before publishing"
        }
    }

    var icon: String {
        switch self {
        case .videoSummarization: return "doc.text"
        case .shortsClips: return "scissors.circle"
        case .contentRecreation: return "wand.and.stars"
        case .contentFreshness: return "sparkles"
        case .performancePredictor: return "chart.line.uptrend.xyaxis"
        }
    }
    
    var infoText: String {
        switch self {
        case .videoSummarization:
            return "Video Summarization uses AI to generate concise summaries of your videos, making it easier for viewers to understand the main points quickly."
        case .shortsClips:
            return "Shorts & Clips Creation automatically generates short-form clips from your videos, perfect for sharing on social media platforms."
        case .contentRecreation:
            return "Content Recreation transforms your existing content into different formats, helping you reach new audiences and repurpose your material."
        case .contentFreshness:
            return "Content Freshness Score analyzes the uniqueness and originality of your content, giving you insights into how fresh your video is compared to others."
        case .performancePredictor:
            return "Performance Predictor uses AI to estimate how your video will perform before publishing, based on title, description, and other factors."
        }
    }
}


struct AIOptionsCard: View {
    let iconName: String
    let title: String
    let subtitle: String
    var isSelected: Bool = false
    var infoText: String? = nil

    var body: some View {
        HStack(alignment: .center, spacing: 16) {
            ZStack {
                Circle()
                    .fill(isSelected ? AppColor.accentBlue.color.opacity(0.25) : AppColor.darkGrayBackground.color.opacity(0.5))
                    .frame(width: 40, height: 40)
                Image(systemName: iconName)
                    .font(AppFontStyle.title2.style)
                    .foregroundColor(isSelected ? .white : AppColor.accentBlue.color)
            }
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 6) {
                    Text(title)
                        .font(AppFontStyle.title2.style.weight(.semibold))
                        .foregroundColor(AppColor.primary.color)
                    if let infoText = infoText {
                        InfoPopoverButton(text: infoText, popoverTitle: "About this AI Option")
                    }
                }
                Text(subtitle)
                    .font(AppFontStyle.body.style.weight(.medium))
                    .foregroundColor(AppColor.grayText.color)
                    .transition(.opacity)
            }
            Spacer()
            Image(systemName: isSelected ? "chevron.up" : "chevron.down")
                .font(AppFontStyle.title2.style)
                .foregroundColor(AppColor.grayText.color)
                .animation(.easeInOut, value: isSelected)
        }
        .padding(16)
        .background(isSelected ? AppColor.accentBlue.color.opacity(0.25) : AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(isSelected ? AppColor.accentBlue.color.opacity(0.4) : AppColor.grayText.color.opacity(0.08), lineWidth: 1)
        )
        .shadow(color: AppColor.grayText.color.opacity(0.06), radius: 4, x: 0, y: 2)
        .animation(.easeInOut, value: isSelected)
    }
}

// Usage Example for Preview
#Preview {
    VStack(spacing: 16) {
        AIOptionsCard(iconName: "doc.text", title: "Smart Transcription", subtitle: "Create accurate transcripts", isSelected: false, infoText: AIOption.videoSummarization.infoText)
        AIOptionsCard(iconName: "sparkles", title: "Content Freshness", subtitle: "Analyze uniqueness and originality", isSelected: true, infoText: AIOption.contentFreshness.infoText)
        AIOptionsCard(iconName: "chart.line.uptrend.xyaxis", title: "Performance Predictor", subtitle: "Predict video performance", isSelected: false, infoText: AIOption.performancePredictor.infoText)
    }
    .background(AppColor.darkBackground.color)
    .padding()
}
